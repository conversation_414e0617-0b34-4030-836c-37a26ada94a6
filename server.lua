local ESX = nil
CreateThread(function()
    while ESX == nil do
        ESX = exports["es_extended"]:getSharedObject()
        Wait(0)
    end
end)
local baggedPlayers = {}

-- Debug function
local function Debug(msg)
    if Config.Debug then
        print('[BD_BODYBAG] ' .. msg)
    end
end

-- Register items
CreateThread(function()
    -- Register bodybag item
    ESX.RegisterUsableItem(Config.BodyBagItem, function(source)
        TriggerClientEvent('bd_bodybag:client:useBodyBag', source)
    end)

    Debug('Bodybag item registered')
end)

-- Check if player has shovel
CreateThread(function()
    while ESX == nil do Wait(0) end

    ESX.RegisterServerCallback('bd_bodybag:server:hasShovel', function(source, cb)
        local xPlayer = ESX.GetPlayerFromId(source)
        local hasShovel = xPlayer.getInventoryItem(Config.Shovel.ShovelItem).count > 0
        cb(hasShovel)
    end)
end)

-- Bodybag a player
RegisterServerEvent('bd_bodybag:server:bodyBagPlayer', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    if not xTarget then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'Player not found',
            type = 'error'
        })
        return
    end

    -- Check if player has bodybag item
    local item = xPlayer.getInventoryItem(Config.BodyBagItem)
    if item.count <= 0 then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'You don\'t have a bodybag',
            type = 'error'
        })
        return
    end

    -- Remove bodybag item
    xPlayer.removeInventoryItem(Config.BodyBagItem, 1)

    -- Add player to bagged players
    baggedPlayers[targetId] = {
        bagger = source,
        time = os.time()
    }

    -- Trigger client event
    TriggerClientEvent('bd_bodybag:client:bodyBagged', targetId)

    TriggerClientEvent('ox_lib:notify', source, {
        title = 'Success',
        description = 'Player has been bodybagged',
        type = 'success'
    })

    Debug('Player ' .. targetId .. ' has been bodybagged by ' .. source)
end)

-- Bury a body (self)
RegisterServerEvent('bd_bodybag:server:buryBody', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local targetFound = false

    -- Find the player who is being buried
    for targetId, data in pairs(baggedPlayers) do
        -- Check if the player is burying themselves (they are the target)
        if tonumber(targetId) == tonumber(source) then
            targetFound = true

            -- Handle character deletion if enabled
            if Config.DeleteCharacter then
                -- Get player identifier
                local identifier = xPlayer.identifier
                Debug('Deleting character with identifier: ' .. identifier)

                -- Delete player from users table
                MySQL.Async.execute('DELETE FROM users WHERE identifier = ?', {
                    identifier
                }, function(rowsChanged)
                    Debug('Deleted ' .. rowsChanged .. ' rows from users table')
                end)

                -- Delete player owned vehicles if enabled
                if Config.DeletePlayerOwnedVehicle then
                    MySQL.Async.execute('DELETE FROM owned_vehicles WHERE owner = ?', {
                        identifier
                    }, function(rowsChanged)
                        Debug('Deleted ' .. rowsChanged .. ' rows from owned_vehicles table')
                    end)
                end

                -- Trigger event to clean up bodybag before kicking
                TriggerClientEvent('bd_bodybag:client:beingBuried', source)
                Wait(500) -- Give time for the client to process

                -- Broadcast news about the burial
                BroadcastBurialNews(source)

                -- Kick the player
                DropPlayer(source, 'You have been buried alive and your character has been deleted')
                Debug('Player ' .. source .. ' has been kicked')
            end

            -- Remove from bagged players
            baggedPlayers[targetId] = nil

            -- Remove shovel item from player's inventory after burying themselves
            if Config.Shovel.required then
                local shovelItem = xPlayer.getInventoryItem(Config.Shovel.ShovelItem)
                if shovelItem and shovelItem.count > 0 then
                    xPlayer.removeInventoryItem(Config.Shovel.ShovelItem, 1)
                    Debug('Removed shovel from player ' .. source .. ' after burying themselves')
                end
            end

            Debug('Player ' .. targetId .. ' has buried themselves')
            break
        end
    end

    if not targetFound then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'No body to bury',
            type = 'error'
        })
    end
end)

-- Bury a target player
RegisterServerEvent('bd_bodybag:server:buryTargetPlayer', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    if not xTarget then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'Target player not found',
            type = 'error'
        })
        return
    end

    -- Handle character deletion if enabled
    if Config.DeleteCharacter then
        -- Get player identifier
        local identifier = xTarget.identifier
        Debug('Deleting character with identifier: ' .. identifier)

        -- Delete player from users table
        MySQL.Async.execute('DELETE FROM users WHERE identifier = ?', {
            identifier
        }, function(rowsChanged)
            Debug('Deleted ' .. rowsChanged .. ' rows from users table')
        end)

        -- Delete player owned vehicles if enabled
        if Config.DeletePlayerOwnedVehicle then
            MySQL.Async.execute('DELETE FROM owned_vehicles WHERE owner = ?', {
                identifier
            }, function(rowsChanged)
                Debug('Deleted ' .. rowsChanged .. ' rows from owned_vehicles table')
            end)
        end

        -- Trigger event to clean up bodybag before kicking
        TriggerClientEvent('bd_bodybag:client:beingBuried', targetId)
        Wait(500) -- Give time for the client to process

        -- Broadcast news about the burial
        BroadcastBurialNews(targetId)

        -- Kick the player
        DropPlayer(targetId, 'You have been buried alive and your character has been deleted')
        Debug('Player ' .. targetId .. ' has been kicked')
    end

    -- Remove from bagged players if they were bagged
    if baggedPlayers[targetId] then
        baggedPlayers[targetId] = nil
        Debug('Removed player ' .. targetId .. ' from bagged players')
    end

    -- Remove shovel item from player's inventory after burying
    if Config.Shovel.required then
        local shovelItem = xPlayer.getInventoryItem(Config.Shovel.ShovelItem)
        if shovelItem and shovelItem.count > 0 then
            xPlayer.removeInventoryItem(Config.Shovel.ShovelItem, 1)
            Debug('Removed shovel from player ' .. source .. ' after burying')

            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Shovel Used',
                description = 'Your shovel broke while burying the body',
                type = 'inform'
            })
        end
    end

    -- Trigger client event to remove bodybag
    TriggerClientEvent('bd_bodybag:client:removeBodyBag', targetId)
    Debug('Triggered removeBodyBag event for player ' .. targetId)

    Debug('Player ' .. targetId .. ' has been buried by ' .. source)
end)

-- Remove bodybag from a player
RegisterServerEvent('bd_bodybag:server:removeBodyBagFromPlayer', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    if not xTarget then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'Player not found',
            type = 'error'
        })
        return
    end

    -- Check if target is bagged
    if not baggedPlayers[targetId] then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Error',
            description = 'This player is not in a bodybag',
            type = 'error'
        })
        return
    end

    -- Remove from bagged players
    baggedPlayers[targetId] = nil

    -- Trigger client event to remove bodybag
    TriggerClientEvent('bd_bodybag:client:removeBodyBag', targetId)

    -- Notify the player who removed the bodybag
    TriggerClientEvent('ox_lib:notify', source, {
        title = 'Success',
        description = 'You removed the bodybag',
        type = 'success'
    })

    -- Notify the player who was in the bodybag
    TriggerClientEvent('ox_lib:notify', targetId, {
        title = 'Bodybag Removed',
        description = 'Someone removed your bodybag',
        type = 'inform'
    })

    Debug('Player ' .. source .. ' removed bodybag from player ' .. targetId)
end)

-- Player disconnected
AddEventHandler('playerDropped', function(reason)
    local source = source

    -- Check if player is bagged
    for targetId, data in pairs(baggedPlayers) do
        if tonumber(targetId) == tonumber(source) then
            -- Remove from bagged players
            baggedPlayers[targetId] = nil
            Debug('Bagged player ' .. targetId .. ' disconnected')
        end
    end
end)

-- Handle player revival (wasabi_ambulance)
RegisterServerEvent('wasabi_ambulance:revived', function(target)
    HandlePlayerRevive(target)
end)

-- Handle player revival (ESX)
RegisterServerEvent('esx_ambulancejob:revive', function(target)
    HandlePlayerRevive(target)
end)

-- Handle player revival (generic)
function HandlePlayerRevive(target)
    Debug('Revive event triggered for player ' .. tostring(target))

    -- If target is nil, use source
    if not target then
        target = source
        Debug('No target specified, using source: ' .. target)
    end

    -- Always trigger the client event to fix visibility, regardless of bagged state
    TriggerClientEvent('bd_bodybag:client:fixVisibility', target)

    -- Check if player is bagged (convert to string for comparison)
    local targetStr = tostring(target)

    -- Check if player is in baggedPlayers table and remove if found
    for id, data in pairs(baggedPlayers) do
        if tostring(id) == targetStr then
            -- Remove from bagged players
            baggedPlayers[id] = nil
            Debug('Player ' .. target .. ' removed from baggedPlayers table')
            break
        end
    end

    Debug('Player ' .. target .. ' has been revived and visibility fixed')
end

-- Give shovel to player
RegisterServerEvent('bd_bodybag:server:giveShovel', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer then
        -- Add the shovel item to the player's inventory
        xPlayer.addInventoryItem(Config.Shovel.ShovelItem, 1)
        Debug('Gave shovel to player ' .. source)
    end
end)

-- Broadcast news about a burial
function BroadcastBurialNews(targetId)
    -- Check if news display is enabled in config
    if not Config.NewsDisplay.enabled then
        Debug('News display is disabled in config, skipping broadcast')
        return
    end

    local xTarget = ESX.GetPlayerFromId(targetId)
    local targetName = "Unknown"

    if xTarget then
        targetName = xTarget.getName() or "Unknown"
    end

    -- Create news message using config values
    local title = Config.NewsDisplay.title
    local msg = Config.NewsDisplay.message
    local bottom = string.format(Config.NewsDisplay.bottomTemplate, targetName)
    local time = Config.NewsDisplay.displayTime

    -- Trigger the news display
    TriggerEvent("cfx-tcd-newsDisplay:Server:Broadcast", title, msg, bottom, time)
    Debug('Broadcasting news about burial of player ' .. targetId .. ' (' .. targetName .. ')')
end

-- Server callback to check if a player is bodybagged
CreateThread(function()
    while ESX == nil do Wait(0) end

    -- Register server callback for other scripts to check if a player is bodybagged
    ESX.RegisterServerCallback('bd_bodybag:server:isPlayerBagged', function(source, cb, targetId)
        -- If no targetId is provided, check the source
        if not targetId then
            targetId = source
        end

        -- Convert to string for comparison
        local targetStr = tostring(targetId)
        local isBagged = false

        -- Check if player is in baggedPlayers table
        for id, _ in pairs(baggedPlayers) do
            if tostring(id) == targetStr then
                isBagged = true
                break
            end
        end

        cb(isBagged)
    end)
end)

-- Server event for other scripts to check if a player is bodybagged
RegisterNetEvent('bd_bodybag:server:checkPlayerBagged', function(targetId, cb)
    local source = source

    -- If no targetId is provided, check the source
    if not targetId then
        targetId = source
    end

    -- Convert to string for comparison
    local targetStr = tostring(targetId)
    local isBagged = false

    -- Check if player is in baggedPlayers table
    for id, _ in pairs(baggedPlayers) do
        if tostring(id) == targetStr then
            isBagged = true
            break
        end
    end

    -- If callback is provided, call it with the result
    if cb and type(cb) == 'function' then
        cb(isBagged)
    end

    -- Return the result to the client
    TriggerClientEvent('bd_bodybag:client:playerBaggedResult', source, targetId, isBagged)
end)

-- Add admin commands
CreateThread(function()
    while ESX == nil do Wait(0) end

    -- Command to check bagged players (admin only)
    ESX.RegisterCommand('checkbagged', 'admin', function(xPlayer, args, showError)
        local count = 0
        for targetId, data in pairs(baggedPlayers) do
            count = count + 1
            xPlayer.triggerEvent('ox_lib:notify', {
                title = 'Bagged Player',
                description = 'ID: ' .. targetId .. ' | Bagged by: ' .. data.bagger .. ' | Time: ' .. os.date('%H:%M:%S', data.time),
                type = 'inform'
            })
        end

        if count == 0 then
            xPlayer.triggerEvent('ox_lib:notify', {
                title = 'Bagged Players',
                description = 'No players are currently bodybagged',
                type = 'inform'
            })
        end
    end, false)

    -- Command to force fix a player's state (admin only)
    ESX.RegisterCommand('fixbodybag', 'admin', function(xPlayer, args, showError)
        local targetId = args.playerId

        if not targetId then
            showError('You must specify a player ID')
            return
        end

        -- Remove from bagged players if they were bagged
        if baggedPlayers[targetId] then
            baggedPlayers[targetId] = nil
            Debug('Admin removed player ' .. targetId .. ' from bagged players')
        end

        -- Trigger client event to fix visibility
        TriggerClientEvent('bd_bodybag:client:fixVisibility', targetId)

        xPlayer.triggerEvent('ox_lib:notify', {
            title = 'Fix Bodybag',
            description = 'Player ' .. targetId .. ' has been fixed',
            type = 'success'
        })
    end, true, {help = 'Fix a player who is stuck in bodybag state', arguments = {
        {name = 'playerId', help = 'The ID of the player to fix', type = 'number'}
    }})
end)
