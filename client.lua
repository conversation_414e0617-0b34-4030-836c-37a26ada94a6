local ESX = nil
CreateThread(function()
    while ESX == nil do
        ESX = exports["es_extended"]:getSharedObject()
        Wait(0)
    end
end)
local attachedBag = nil
local isBagged = false
local isBuried = false
local dumpZones = {}
local currentZone = nil

-- Debug function
local function Debug(msg)
    if Config.Debug then
        print('[BD_BODYBAG] ' .. msg)
    end
end

-- Check if player is dead
local function IsPlayerDead(playerId)
    if not playerId then
        return exports.wasabi_ambulance:isPlayerDead()
    else
        return exports.wasabi_ambulance:isPlayerDead(playerId)
    end
end

-- Get closest player
local function GetClosestPlayer()
    local players = ESX.Game.GetPlayers()
    local closestDistance = Config.CheckDistance
    local closestPlayer = -1
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    for i = 1, #players, 1 do
        local target = GetPlayerPed(players[i])
        if target ~= playerPed then
            local targetCoords = GetEntityCoords(target)
            local distance = #(coords - targetCoords)
            if distance < closestDistance then
                closestPlayer = players[i]
                closestDistance = distance
            end
        end
    end
    return closestPlayer, closestDistance
end

-- Attach prop to player
local function AttachEntityToPed(prop, bone_ID, x, y, z, RotX, RotY, RotZ)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    if not IsEntityAttached(prop) then
        -- Make sure the prop is visible before attaching
        SetEntityVisible(prop, true, 0)
        SetEntityAlpha(prop, 255, false)

        -- Attach the prop to the player with fixed rotation (flat)
        -- Setting syncRot to false to ignore entity rotation
        AttachEntityToEntity(prop, playerPed, GetPedBoneIndex(playerPed, bone_ID), x, y, z, 0.0, 0.0, 0.0, true, true, false, true, 1, false)

        -- Force the prop to be flat by setting its rotation directly
        SetEntityRotation(prop, 0.0, 0.0, 0.0, 2, true)

        -- Make sure the prop is still visible after attaching
        SetEntityVisible(prop, true, 0)
        SetEntityAlpha(prop, 255, false)

        Debug('Prop attached to player with flat orientation and visibility maintained')
    end
end

-- Keep bodybag flat regardless of player position
local function KeepBodyBagFlat()
    if attachedBag and DoesEntityExist(attachedBag) and isBagged then
        -- Force the bodybag to be flat
        SetEntityRotation(attachedBag, 0.0, 0.0, 0.0, 2, true)
        Debug('Forced bodybag to stay flat')
    end
end

-- Create and attach bodybag prop
local function CreateBodyBag()
    local playerPed = PlayerPedId()

    -- Clean up any existing bodybag
    if attachedBag and DoesEntityExist(attachedBag) then
        SetEntityAsMissionEntity(attachedBag, true, true)
        DeleteEntity(attachedBag)
        attachedBag = nil
        Debug('Deleted existing bodybag')
    end

    -- Load the bodybag model
    local model = GetHashKey(Config.BodyBagProp)
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end

    -- Get player position
    local coords = GetEntityCoords(playerPed)

    -- Create the bodybag object at player position with visibility flags
    attachedBag = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)
    Debug('Created bodybag object: ' .. tostring(attachedBag))

    -- Make sure the bodybag is properly networked
    local netId = NetworkGetNetworkIdFromEntity(attachedBag)
    NetworkRegisterEntityAsNetworked(attachedBag)
    SetNetworkIdCanMigrate(netId, true)
    SetNetworkIdExistsOnAllMachines(netId, true)

    -- Set the bodybag as a mission entity so it doesn't get garbage collected
    SetEntityAsMissionEntity(attachedBag, true, true)

    -- CRITICAL: Set the bodybag to be visible to everyone
    SetEntityVisible(attachedBag, true, 0)
    SetEntityAlpha(attachedBag, 255, false)
    NetworkSetEntityInvisibleToNetwork(attachedBag, false)
    SetEntityLocallyVisible(attachedBag, true)
    SetEntityLocallyInvisible(attachedBag, false)

    -- Attach the bodybag to the player
    AttachEntityToPed(attachedBag, 0, 0.0, 0.0, -0.2, 0.0, 0.0, 0.0)
    SetModelAsNoLongerNeeded(model)

    -- Force the bodybag to be flat immediately after attaching
    KeepBodyBagFlat()

    -- CRITICAL: Make only the player invisible, not the bodybag
    -- We need to set the player invisible AFTER attaching the bodybag
    SetEntityVisible(playerPed, false, 0)
    NetworkSetEntityInvisibleToNetwork(playerPed, true)
    SetEntityLocallyInvisible(playerPed, true)
    SetEntityLocallyVisible(playerPed, false)

    -- Set state
    isBagged = true

    -- Force player into ragdoll state immediately
    SetPedToRagdoll(playerPed, 1000, 1000, 0, 0, 0, 0)

    -- Prevent player from getting up
    SetPedCanRagdoll(playerPed, false)

    -- Verify the bodybag is still visible
    Wait(100)
    if DoesEntityExist(attachedBag) then
        -- Force visibility again after a short delay
        SetEntityVisible(attachedBag, true, 0)
        SetEntityAlpha(attachedBag, 255, false)
        Debug('Bodybag visibility verified')
    end

    -- Trigger an event that other scripts can listen for
    TriggerEvent('bd_bodybag:client:playerFullyDead', true)

    Debug('Bodybag created and player made invisible')

    -- Create a thread to ensure the bodybag stays visible
    CreateThread(function()
        local checkCount = 0
        while attachedBag and DoesEntityExist(attachedBag) and isBagged and checkCount < 10 do
            SetEntityVisible(attachedBag, true, 0)
            SetEntityAlpha(attachedBag, 255, false)
            Wait(500)
            checkCount = checkCount + 1
        end
    end)
end

-- Remove bodybag with animation
local function RemoveBodyBagWithAnim(target)
    local targetId = nil
    if target then
        targetId = GetPlayerServerId(target)
    end

    -- Request animation dictionary
    local dict = Config.RemoveBodyBag.anim.dict
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end

    if exports.ox_lib:progressBar({
        duration = Config.RemoveBodyBag.time,
        label = Config.RemoveBodyBag.label,
        useWhileDead = Config.RemoveBodyBag.useWhileDead,
        canCancel = Config.RemoveBodyBag.canCancel,
        disable = Config.RemoveBodyBag.disable,
        anim = {
            dict = Config.RemoveBodyBag.anim.dict,
            clip = Config.RemoveBodyBag.anim.clip,
            flag = Config.RemoveBodyBag.anim.flag or 1,
        },
    }) then
        if targetId then
            -- Clear the cache for this player
            if _G.bodybagChecks and _G.bodybagChecks[targetId] then
                _G.bodybagChecks[targetId] = nil
                Debug('Cleared bodybag check cache for player ' .. targetId)
            end

            -- We're removing the bodybag from another player
            TriggerServerEvent('bd_bodybag:server:removeBodyBagFromPlayer', targetId)
            Debug('Removing bodybag from player ' .. targetId)
        else
            -- We're removing our own bodybag (shouldn't happen normally, but just in case)
            RemoveBodyBag()
        end

        exports.ox_lib:notify({
            title = 'Success',
            description = 'Body bag removed successfully',
            type = 'success'
        })
    else
        exports.ox_lib:notify({
            title = 'Cancelled',
            description = 'You cancelled the action',
            type = 'error'
        })
    end

    -- Clean up animation dictionary
    RemoveAnimDict(dict)
end

-- Remove bodybag (internal function, no animation)
local function RemoveBodyBag()
    -- Delete the bodybag entity if it exists
    if attachedBag then
        -- Ensure the entity is properly deleted from the network
        SetEntityAsMissionEntity(attachedBag, true, true)
        NetworkRegisterEntityAsNetworked(attachedBag)
        NetworkRequestControlOfEntity(attachedBag)

        -- Wait for control
        local timeout = 0
        while not NetworkHasControlOfEntity(attachedBag) and timeout < 50 do
            Wait(10)
            timeout = timeout + 1
        end

        -- Delete the entity
        DeleteEntity(attachedBag)
        attachedBag = nil
        Debug('Bodybag entity deleted')
    end

    -- Make player fully visible again
    local playerPed = PlayerPedId()
    SetEntityVisible(playerPed, true, 0)
    SetEntityAlpha(playerPed, 255, false) -- Ensure full visibility
    ResetEntityAlpha(playerPed)

    -- Reset state
    isBagged = false

    -- Allow player to ragdoll again
    SetPedCanRagdoll(playerPed, true)

    -- Trigger an event that other scripts can listen for
    TriggerEvent('bd_bodybag:client:playerFullyDead', false)

    Debug('Bodybag removed and player made visible')
end

-- Bodybag a player
local function BodyBagPlayer(target)
    local targetId = GetPlayerServerId(target)

    if not IsPlayerDead(targetId) then
        exports.ox_lib:notify({
            title = 'Error',
            description = 'This player is not dead',
            type = 'error'
        })
        return
    end

    -- Request animation dictionary
    local dict = Config.BodyBag.anim.dict
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end

    if exports.ox_lib:progressBar({
        duration = Config.BodyBag.time,
        label = Config.BodyBag.label,
        useWhileDead = Config.BodyBag.useWhileDead,
        canCancel = Config.BodyBag.canCancel,
        disable = Config.BodyBag.disable,
        anim = {
            dict = Config.BodyBag.anim.dict,
            clip = Config.BodyBag.anim.clip,
            flag = Config.BodyBag.anim.flag or 1,
        },
    }) then
        TriggerServerEvent('bd_bodybag:server:bodyBagPlayer', targetId)
        Debug('Bodybagging player ' .. targetId)
    else
        exports.ox_lib:notify({
            title = 'Cancelled',
            description = 'You cancelled the action',
            type = 'error'
        })
    end

    -- Clean up animation dictionary
    RemoveAnimDict(dict)
end

-- Bury a body
local function BuryBody(targetEntity)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local canBury = false
    local targetPlayer = nil

    -- Check if we're burying a specific target or ourselves
    if targetEntity then
        -- We're burying another player
        targetPlayer = NetworkGetPlayerIndexFromPed(targetEntity)
        if not targetPlayer then
            exports.ox_lib:notify({
                title = 'Error',
                description = 'Invalid target',
                type = 'error'
            })
            return
        end

        -- Check if target is dead
        local targetId = GetPlayerServerId(targetPlayer)
        if not IsPlayerDead(targetId) then
            exports.ox_lib:notify({
                title = 'Error',
                description = 'Target is not dead',
                type = 'error'
            })
            return
        end

        Debug('Attempting to bury player ' .. targetId)
    else
        -- We're burying ourselves
        if not isBagged then
            exports.ox_lib:notify({
                title = 'Error',
                description = 'No body to bury',
                type = 'error'
            })
            return
        end
    end

    -- Check if in burial zone
    if Config.BuryLocations.enabled then
        for k, v in pairs(Config.BuryLocations.locations) do
            local distance = #(coords - v.coords)
            if distance <= v.radius then
                canBury = true
                break
            end
        end

        if not canBury then
            exports.ox_lib:notify({
                title = 'Error',
                description = 'You cannot bury a body here',
                type = 'error'
            })
            return
        end
    end

    -- Check if player has shovel
    if Config.Shovel.required then
        ESX.TriggerServerCallback('bd_bodybag:server:hasShovel', function(hasShovel)
            if not hasShovel then
                exports.ox_lib:notify({
                    title = 'Error',
                    description = 'You need a shovel to bury a body',
                    type = 'error'
                })
                return
            end

            PerformBurying(targetPlayer)
        end)
    else
        PerformBurying(targetPlayer)
    end
end

-- Perform the burying action
function PerformBurying(targetPlayer)
    -- Request the animation dictionary
    local dict = Config.Burying.anim.dict
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end

    -- Create and attach shovel prop if configured
    local shovelProp = nil
    if Config.Burying.prop then
        -- Request the model
        local model = Config.Burying.prop.model
        if type(model) == 'string' then
            model = GetHashKey(model)
        end

        RequestModel(model)
        while not HasModelLoaded(model) do
            Wait(10)
        end

        -- Create the prop
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        shovelProp = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)

        -- Attach the prop to the player
        AttachEntityToEntity(
            shovelProp,
            playerPed,
            GetPedBoneIndex(playerPed, Config.Burying.prop.bone),
            Config.Burying.prop.pos.x, Config.Burying.prop.pos.y, Config.Burying.prop.pos.z,
            Config.Burying.prop.rot.x, Config.Burying.prop.rot.y, Config.Burying.prop.rot.z,
            true, true, false, true, 1, true
        )

        Debug('Created and attached shovel prop for burying animation')
    end

    if exports.ox_lib:progressBar({
        duration = Config.Burying.time,
        label = Config.Burying.label,
        useWhileDead = Config.Burying.usewhileDead,
        canCancel = Config.Burying.canCancel,
        disable = Config.Burying.disable,
        anim = {
            dict = Config.Burying.anim.dict,
            clip = Config.Burying.anim.clip,
            flag = Config.Burying.anim.flag or 1,
        },
    }) then
        -- Delete the shovel prop if it was created
        if shovelProp and DoesEntityExist(shovelProp) then
            DeleteEntity(shovelProp)
            Debug('Deleted shovel prop after burying animation')
        end

        if targetPlayer then
            -- We're burying another player
            local targetId = GetPlayerServerId(targetPlayer)
            TriggerServerEvent('bd_bodybag:server:buryTargetPlayer', targetId)
            Debug('Burying target player ' .. targetId)
        else
            -- We're burying ourselves
            TriggerServerEvent('bd_bodybag:server:buryBody')
            RemoveBodyBag()
            isBuried = true
            Debug('Burying self')
        end

        exports.ox_lib:notify({
            title = 'Success',
            description = 'Body buried successfully',
            type = 'success'
        })

        Debug('Body buried successfully')
    else
        -- Delete the shovel prop if it was created (on cancel)
        if shovelProp and DoesEntityExist(shovelProp) then
            DeleteEntity(shovelProp)
            Debug('Deleted shovel prop after cancelling burying animation')
        end

        exports.ox_lib:notify({
            title = 'Cancelled',
            description = 'You cancelled the action',
            type = 'error'
        })
    end

    -- Clean up animation dictionary
    RemoveAnimDict(dict)
end

-- Handle being buried by another player
RegisterNetEvent('bd_bodybag:client:beingBuried', function()
    -- Make sure to remove the bodybag and reset state before being kicked
    RemoveBodyBag()
    isBagged = false
    isBuried = true
    Debug('Player is being buried, cleaned up bodybag')
end)

-- Create dump zones
local function CreateDumpZones()
    if Config.BuryLocations.enabled then
        for k, v in pairs(Config.BuryLocations.locations) do
            -- Create a thread to check if player is in the zone
            CreateThread(function()
                local zoneActive = false
                while true do
                    Wait(500)
                    local playerPed = PlayerPedId()
                    local coords = GetEntityCoords(playerPed)
                    local distance = #(coords - v.coords)

                    -- Check if player entered the zone
                    if distance <= v.radius and not zoneActive then
                        zoneActive = true
                        -- Show notification when entering the zone
                        exports.ox_lib:notify({
                            title = Config.BuryLocations.notification.title,
                            description = Config.BuryLocations.notification.description,
                            type = Config.BuryLocations.notification.type
                        })

                        if isBagged and not isBuried then
                            currentZone = k
                            exports.ox_lib:showTextUI('[E] - Bury Player')
                        end
                    -- Check if player left the zone
                    elseif distance > v.radius and zoneActive then
                        zoneActive = false
                        if currentZone == k then
                            currentZone = nil
                            exports.ox_lib:hideTextUI()
                        end
                    end
                end
            end)

            -- Store zone info for debugging
            dumpZones[k] = {
                coords = v.coords,
                radius = v.radius
            }

            -- Draw marker for the zone (always visible)
            CreateThread(function()
                while true do
                    Wait(0)
                    -- Use hardcoded marker type and color values instead of reading from config
                    DrawMarker(
                        1, -- Type (1 = cylinder)
                        v.coords.x, v.coords.y, v.coords.z - 1.0, -- Position (slightly below ground)
                        0.0, 0.0, 0.0, -- Direction
                        0.0, 0.0, 0.0, -- Rotation
                        5.0, 5.0, 1.5, -- Fixed scale values (x, y, z)
                        255, 0, 0, 100, -- Red color with 100 alpha
                        false, false, 2, nil, nil, false -- Additional settings
                    )
                end
            end)
        end
        Debug('Created burial zones with circle markers')
    end
end

-- Create blips for burial locations
local function CreateBuryBlips()
    if Config.BuryLocations.enabled and Config.BuryLocations.blip and Config.BuryLocations.blip.enabled then
        for k, v in pairs(Config.BuryLocations.locations) do
            local blip = AddBlipForCoord(v.coords.x, v.coords.y, v.coords.z)
            SetBlipSprite(blip, Config.BuryLocations.blip.sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, Config.BuryLocations.blip.scale)
            SetBlipColour(blip, Config.BuryLocations.blip.color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(Config.BuryLocations.blip.name)
            EndTextCommandSetBlipName(blip)
            Debug('Created blip for burial location: ' .. k)
        end
        Debug('Created blips for all burial locations')
    end
end

-- Initialize ox_target
local function InitOxTarget()
    if Config.System == 'ox_target' then
        exports.ox_target:addGlobalPlayer({
            {
                name = 'bd_bodybag:bodyBagPlayer',
                icon = 'fas fa-box',
                label = 'Bodybag Player',
                canInteract = function(entity, distance, coords, name)
                    if distance > Config.CheckDistance then return false end

                    local targetPed = entity
                    local targetPlayer = NetworkGetPlayerIndexFromPed(targetPed)
                    if targetPlayer then
                        local targetId = GetPlayerServerId(targetPlayer)
                        local isDead = IsPlayerDead(targetId)
                        Debug('Target ID: ' .. targetId .. ' | Is Dead: ' .. tostring(isDead))
                        return isDead
                    end
                    return false
                end,
                onSelect = function(data)
                    local target = NetworkGetPlayerIndexFromPed(data.entity)
                    BodyBagPlayer(target)
                end,
                items = Config.BodyBagItem
            },
            {
                name = 'bd_bodybag:removeBodyBag',
                icon = 'fas fa-box-open',
                label = 'Remove Bodybag',
                canInteract = function(entity, distance, coords, name)
                    if distance > Config.CheckDistance then return false end

                    local targetPed = entity
                    local targetPlayer = NetworkGetPlayerIndexFromPed(targetPed)
                    if targetPlayer then
                        local targetId = GetPlayerServerId(targetPlayer)

                        -- First check if the player is dead
                        if not IsPlayerDead(targetId) then
                            return false
                        end

                        -- Check if the player has a visible bodybag attached
                        -- This is a client-side check that can help filter out obvious non-bagged players
                        local isVisiblyBagged = false
                        local targetCoords = GetEntityCoords(targetPed)

                        -- Check for any bodybag props near the player
                        local objects = GetGamePool('CObject')
                        for _, object in ipairs(objects) do
                            if DoesEntityExist(object) and GetEntityModel(object) == GetHashKey(Config.BodyBagProp) then
                                local objectCoords = GetEntityCoords(object)
                                local distance = #(targetCoords - objectCoords)
                                if distance < 2.0 then
                                    isVisiblyBagged = true
                                    break
                                end
                            end
                        end

                        -- If we can see a bodybag, allow the interaction
                        if isVisiblyBagged then
                            return true
                        end

                        -- If we can't see a bodybag, do a server-side check
                        -- We'll use a cached result to avoid spamming the server

                        -- Store the check data on the global table instead of the entity
                        -- This is more reliable as entity references can change
                        if not _G.bodybagChecks then
                            _G.bodybagChecks = {}
                        end

                        -- Check if we need to refresh the cached result
                        if not _G.bodybagChecks[targetId] or (GetGameTimer() - (_G.bodybagChecks[targetId].lastCheck or 0) > 5000) then
                            -- Initialize or update the check time
                            if not _G.bodybagChecks[targetId] then
                                _G.bodybagChecks[targetId] = {
                                    isInBodybag = false,
                                    lastCheck = GetGameTimer()
                                }
                            else
                                _G.bodybagChecks[targetId].lastCheck = GetGameTimer()
                            end

                            -- Check with the server if this player is bagged
                            ESX.TriggerServerCallback('bd_bodybag:server:isPlayerBagged', function(isBagged)
                                _G.bodybagChecks[targetId].isInBodybag = isBagged
                                Debug('Server check for player ' .. targetId .. ' being in bodybag: ' .. tostring(isBagged))
                            end, targetId)

                            -- Return false for now, we'll update next check
                            return false
                        end

                        -- Return the cached result
                        return _G.bodybagChecks[targetId].isInBodybag
                    end
                    return false
                end,
                onSelect = function(data)
                    local target = NetworkGetPlayerIndexFromPed(data.entity)
                    local targetId = GetPlayerServerId(target)

                    -- Double-check with the server before proceeding
                    ESX.TriggerServerCallback('bd_bodybag:server:isPlayerBagged', function(isBagged)
                        if isBagged then
                            -- Clear the cache for this player
                            if _G.bodybagChecks and _G.bodybagChecks[targetId] then
                                _G.bodybagChecks[targetId] = nil
                                Debug('Cleared bodybag check cache for player ' .. targetId)
                            end

                            RemoveBodyBagWithAnim(target)
                        else
                            exports.ox_lib:notify({
                                title = 'Error',
                                description = 'This player is not in a bodybag',
                                type = 'error'
                            })
                        end
                    end, targetId)
                end
            },
            {
                name = 'bd_bodybag:buryPlayer',
                icon = 'fas fa-skull',
                label = 'Bury Player',
                canInteract = function(entity, distance, coords, name)
                    if distance > Config.CheckDistance then return false end

                    -- Check if player is in a burial zone
                    local playerPed = PlayerPedId()
                    local playerCoords = GetEntityCoords(playerPed)
                    local canBury = false

                    if Config.BuryLocations.enabled then
                        for k, v in pairs(Config.BuryLocations.locations) do
                            local distance = #(playerCoords - v.coords)
                            if distance <= v.radius then
                                canBury = true
                                break
                            end
                        end
                    end

                    if not canBury then return false end

                    -- Check if target is dead and bagged
                    local targetPed = entity
                    local targetPlayer = NetworkGetPlayerIndexFromPed(targetPed)
                    if targetPlayer then
                        local targetId = GetPlayerServerId(targetPlayer)
                        local isDead = IsPlayerDead(targetId)
                        -- We can only check if the player is bagged on the server side
                        -- So we'll just check if they're dead for now
                        return isDead
                    end
                    return false
                end,
                onSelect = function(data)
                    BuryBody(data.entity)
                end
            }
        })
        Debug('ox_target initialized with Bodybag, Remove Bodybag, and Bury options')
    end
end

-- Event handlers
RegisterNetEvent('bd_bodybag:client:bodyBagged', function()
    CreateBodyBag()
end)

RegisterNetEvent('bd_bodybag:client:removeBodyBag', function()
    RemoveBodyBag()
end)

-- ESX revive event handler
RegisterNetEvent('esx_ambulancejob:revive', function()
    Debug('ESX revive event received')
    -- Always force fix visibility on revive, regardless of isBagged state
    ForceFixVisibility()
end)

-- Wasabi ambulance revive event handler
RegisterNetEvent('wasabi_ambulance:revive', function()
    Debug('Wasabi ambulance revive event received')
    -- Always force fix visibility on revive, regardless of isBagged state
    ForceFixVisibility()
end)

-- Function to force fix visibility (same as the command but without notification)
function ForceFixVisibility()
    -- Force reset player visibility with multiple methods to ensure it works
    local playerPed = PlayerPedId()

    -- Method 1: Basic visibility
    SetEntityVisible(playerPed, true, 0)

    -- Method 2: Alpha reset
    SetEntityAlpha(playerPed, 255, false)
    ResetEntityAlpha(playerPed)

    -- Method 3: Network visibility
    NetworkSetEntityInvisibleToNetwork(playerPed, false)

    -- Method 4: Local invisibility
    SetEntityLocallyInvisible(playerPed, false)
    SetEntityLocallyVisible(playerPed, true)

    -- Delete any existing bodybag with multiple safety checks
    if attachedBag and DoesEntityExist(attachedBag) then
        -- Set as mission entity to prevent garbage collection issues
        SetEntityAsMissionEntity(attachedBag, true, true)

        -- Request network control
        NetworkRegisterEntityAsNetworked(attachedBag)
        NetworkRequestControlOfEntity(attachedBag)

        -- Wait for control with timeout
        local timeout = 0
        while not NetworkHasControlOfEntity(attachedBag) and timeout < 50 and DoesEntityExist(attachedBag) do
            Wait(10)
            timeout = timeout + 1
        end

        -- Delete the entity
        if DoesEntityExist(attachedBag) then
            DeleteEntity(attachedBag)
            Debug('Bodybag entity deleted during revive')
        end

        attachedBag = nil
    end

    -- Reset state variables
    isBagged = false
    isBuried = false

    Debug('Player visibility has been fixed during revive')

    -- Schedule another visibility check after a short delay
    -- This helps with any race conditions or network sync issues
    SetTimeout(500, function()
        if not isBagged and playerPed and DoesEntityExist(playerPed) then
            SetEntityVisible(playerPed, true, 0)
            SetEntityAlpha(playerPed, 255, false)
            ResetEntityAlpha(playerPed)
        end
    end)
end

-- Fix visibility command handler
RegisterNetEvent('bd_bodybag:client:fixVisibility', function()
    -- Use the same function as revive events
    ForceFixVisibility()

    -- Notify player (only for the command, not for automatic revive)
    exports.ox_lib:notify({
        title = 'Visibility Fixed',
        description = 'Your visibility has been restored',
        type = 'success'
    })
end)

-- Item usage
RegisterNetEvent('esx:onPlayerDeath', function(data)
    isBagged = false
    isBuried = false
    if attachedBag then
        DeleteEntity(attachedBag)
        attachedBag = nil
    end

    -- Ensure player is visible on death
    local playerPed = PlayerPedId()
    SetEntityVisible(playerPed, true, false)
    SetEntityAlpha(playerPed, 255, false)

    Debug('Player died, reset bodybag state and visibility')
end)

-- Key controls
CreateThread(function()
    while true do
        Wait(0)
        if currentZone and isBagged and not isBuried then
            if IsControlJustReleased(0, 38) then -- E key
                BuryBody()
            end
        else
            Wait(500)
        end
    end
end)

-- Create shovel prop with ox_target
local function CreateShovelProp()
    -- Shovel prop model and coordinates
    local model = -1095320058
    local coords = vector3(-1760.06, -261.51, 48.18)
    local heading = 330.08

    -- Request the model
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(10)
    end

    -- Create the shovel prop
    local shovelProp = CreateObject(model, coords.x, coords.y, coords.z, false, false, false)
    SetEntityHeading(shovelProp, heading)
    FreezeEntityPosition(shovelProp, true)
    SetEntityAsMissionEntity(shovelProp, true, true)

    -- Add ox_target to the shovel prop
    exports.ox_target:addLocalEntity(shovelProp, {
        {
            label = 'Pick up Shovel',
            icon = 'fa-solid fa-hand',
            distance = 2.0,
            onSelect = function(data)
                local entity = data.entity

                -- Delete the prop
                DeleteEntity(entity)

                -- Trigger server event to add shovel to inventory
                TriggerServerEvent('bd_bodybag:server:giveShovel')

                -- Notify player
                exports.ox_lib:notify({
                    title = 'Shovel Picked Up',
                    description = 'You picked up a shovel',
                    type = 'success'
                })

                Debug('Player picked up shovel prop')
            end
        }
    })

    Debug('Created shovel prop at ' .. coords.x .. ', ' .. coords.y .. ', ' .. coords.z)
    return shovelProp
end

-- Initialize
CreateThread(function()
    Wait(1000)

    -- Ensure player is visible on script start
    local playerPed = PlayerPedId()
    SetEntityVisible(playerPed, true, 0)
    SetEntityAlpha(playerPed, 255, false)
    ResetEntityAlpha(playerPed)

    -- Reset state variables
    isBagged = false
    isBuried = false

    -- Delete any existing bodybag
    if attachedBag then
        DeleteEntity(attachedBag)
        attachedBag = nil
    end

    InitOxTarget()
    CreateDumpZones()
    CreateBuryBlips()
    CreateShovelProp()
    Debug('BD Bodybag initialized - player visibility reset')
end)

-- Thread to continuously check bodybag visibility and orientation
CreateThread(function()
    while true do
        Wait(500) -- Check every half second for more responsive rotation fixing

        -- Only run checks if player is bagged and bodybag exists
        if isBagged and attachedBag and DoesEntityExist(attachedBag) then
            -- Ensure bodybag is visible
            SetEntityVisible(attachedBag, true, 0)
            SetEntityAlpha(attachedBag, 255, false)
            NetworkSetEntityInvisibleToNetwork(attachedBag, false)

            -- Ensure player remains invisible
            local playerPed = PlayerPedId()
            SetEntityVisible(playerPed, false, 0)

            -- Keep the bodybag flat regardless of player position
            KeepBodyBagFlat()

            Debug('Visibility and orientation check: Bodybag visible and flat, player invisible')
        end
    end
end)

-- Thread to force player to stay dead when bodybagged
CreateThread(function()
    while true do
        Wait(0) -- Need to run every frame for controls to be disabled properly

        if isBagged then
            local playerPed = PlayerPedId()

            -- Disable all movement controls
            DisableControlAction(0, 30, true) -- MoveLeftRight
            DisableControlAction(0, 31, true) -- MoveUpDown
            DisableControlAction(0, 32, true) -- W
            DisableControlAction(0, 33, true) -- S
            DisableControlAction(0, 34, true) -- A
            DisableControlAction(0, 35, true) -- D
            DisableControlAction(0, 36, true) -- LeftCtrl
            DisableControlAction(0, 44, true) -- Q
            DisableControlAction(0, 45, true) -- R
            DisableControlAction(0, 46, true) -- E
            DisableControlAction(0, 47, true) -- G
            DisableControlAction(0, 59, true) -- Shift
            DisableControlAction(0, 71, true) -- W vehicle
            DisableControlAction(0, 72, true) -- S vehicle
            DisableControlAction(0, 75, true) -- Exit Vehicle

            -- Prevent ragdoll state changes
            SetPedCanRagdoll(playerPed, false)

            -- Force player to stay in ragdoll if they're not already
            if not IsPedRagdoll(playerPed) then
                SetPedToRagdoll(playerPed, 1000, 1000, 0, 0, 0, 0)
            end

            -- Disable jumping and other actions
            DisableControlAction(0, 22, true) -- Jump
            DisableControlAction(0, 23, true) -- Enter Vehicle
            DisableControlAction(0, 24, true) -- Attack
            DisableControlAction(0, 25, true) -- Aim
            DisableControlAction(0, 37, true) -- Weapon Wheel
            DisableControlAction(0, 157, true) -- 1
            DisableControlAction(0, 158, true) -- 2
            DisableControlAction(0, 160, true) -- 3
            DisableControlAction(0, 164, true) -- 4
            DisableControlAction(0, 165, true) -- 5
            DisableControlAction(0, 159, true) -- 6
            DisableControlAction(0, 161, true) -- 7
            DisableControlAction(0, 162, true) -- 8
            DisableControlAction(0, 163, true) -- 9

            -- Prevent any form of movement
            SetEntityVelocity(playerPed, 0.0, 0.0, 0.0)
        else
            -- If not bagged, allow ragdoll
            local playerPed = PlayerPedId()
            SetPedCanRagdoll(playerPed, true)
            Wait(500) -- Can wait longer when not bagged
        end
    end
end)

-- Item usage
RegisterNetEvent('bd_bodybag:client:useBodyBag', function()
    local closestPlayer, distance = GetClosestPlayer()
    if closestPlayer ~= -1 and distance <= Config.CheckDistance then
        BodyBagPlayer(closestPlayer)
    else
        exports.ox_lib:notify({
            title = 'Error',
            description = 'No player nearby',
            type = 'error'
        })
    end
end)

-- Exports
exports('isPlayerBagged', function()
    return isBagged
end)

exports('isPlayerBuried', function()
    return isBuried
end)

-- Export to check if player is fully dead (for other scripts)
exports('isPlayerFullyDead', function()
    -- If player is bagged, they should be considered fully dead
    -- This can be used by other scripts to prevent crawling or other movement
    return isBagged or isBuried
end)

-- Event handler for server response about player bagged state
RegisterNetEvent('bd_bodybag:client:playerBaggedResult', function(targetId, isBaggedState)
    -- This event is triggered when another script checks if a player is bagged
    -- You can use this to synchronize state between scripts
    Debug('Received bagged state for player ' .. targetId .. ': ' .. tostring(isBaggedState))

    -- If this is about the local player, we can update our state
    if tonumber(targetId) == GetPlayerServerId(PlayerId()) then
        -- Only update if the states don't match
        if isBagged ~= isBaggedState then
            Debug('Updating local bagged state from ' .. tostring(isBagged) .. ' to ' .. tostring(isBaggedState))
            isBagged = isBaggedState

            -- If we're no longer bagged but we still have a bodybag, remove it
            if not isBagged and attachedBag and DoesEntityExist(attachedBag) then
                RemoveBodyBag()
            end
        end
    end
end)
