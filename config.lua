Config = {}

Config.Debug = false
Config.QBCoreGetCoreObject = 'qb-core'
Config.ESXgetSharedObject = 'es_extended'
Config.CheckDistance = 2.0 -- Distance to check for dead players

Config.System = 'ox_target' -- OPTIONS: 'ox_target', 'qb-target', 'textui'

-- Bodybag
Config.BodyBagItem = 'bodybag' -- Item name for the bodybag

-- Bury
Config.Shovel = {
    required = true, -- true: shovel is required to bury a body | false: no shovel item is required
    ShovelItem = 'dkshovel' -- item name for shovel
}

-- Bodybag
Config.BodyBag = {
    time = 2000, -- Time in milliseconds
    label = 'Placing inside Body Bag...',
    useWhileDead = false,
    canCancel = false,
    anim = {
        dict = 'missexile3',
        clip = 'ex03_dingy_search_case_base_michael',
        flag = 1
    },
    disable = {
        move = true,
        car = true,
        combat = true,
    }
}

-- Props
Config.BodyBagProp = 'xm_prop_body_bag' -- Prop for the body bag

-- Dead Checks
-- Options: 'wasabi', 'ak47', 'ak47qb', 'esx'
Config.DeadChecks = 'wasabi' -- Choose which resource to use for checking if the player is dead

-- Deletion Settings
Config.DeletePlayerOwnedVehicle = true -- Should player-owned vehicles be deleted?
Config.DeleteCharacter = true -- Should the character be deleted?

-- Bury Locations
Config.BuryLocations = {
    enabled = true,
    -- Marker settings are now hardcoded in client.lua
    notification = {
        title = 'Burial Zone',
        description = 'You can bury A Bodybag here',
        type = 'inform'
    },
    blip = {
        enabled = true,
        sprite = 310, -- Skull icon
        color = 1, -- Red color
        scale = 0.8,
        name = 'Burial Site'
    },
    locations = {
        cemetery = {coords = vec3(-1763.2334, -263.1300, 48.1583), radius = 2.5},
        -- Add more locations as necessary
    }
}

-- Burying
Config.Burying = {
    time = 5000, -- Time in milliseconds
    label = 'Burying body...',
    usewhileDead = false,
    canCancel = false,
    anim = {
        dict = 'random@burial',
        clip = 'a_burial',
        flag = 1
    },
    prop = {
        model = `prop_tool_shovel`,
        bone = 28422,
        pos = vec3(0.0, 0.0, 0.24),
        rot = vec3(0.0, 0.0, 0.0)
    },
    disable = {
        move = true,
        car = true,
        combat = true,
    }
}

-- Remove Bodybag
Config.RemoveBodyBag = {
    time = 2000, -- Time in milliseconds
    label = 'Removing Body Bag...',
    useWhileDead = false,
    canCancel = false,
    anim = {
        dict = 'missexile3',
        clip = 'ex03_dingy_search_case_base_michael',
        flag = 1
    },
    disable = {
        move = true,
        car = true,
        combat = true,
    }
}

-- News Display
Config.NewsDisplay = {
    enabled = true, -- Enable news broadcast when a player is buried
    title = "BREAKING NEWS",
    message = "A body has been discovered buried at the cemetery.",
    bottomTemplate = "The victim has been identified as %s.", -- %s will be replaced with the player's name
    displayTime = 10 -- Time in seconds
}
